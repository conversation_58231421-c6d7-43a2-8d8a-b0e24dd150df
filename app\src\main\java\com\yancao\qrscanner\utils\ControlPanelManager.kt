package com.yancao.qrscanner.utils

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.*
import androidx.core.widget.addTextChangedListener
import com.yancao.qrscanner.R
import com.yancao.qrscanner.domain.FrequencyUnit
import com.yancao.qrscanner.domain.ScanConfig

/**
 * 控制面板管理器
 * 负责控制面板的显示、隐藏和参数配置
 */
class ControlPanelManager(
    private val context: Context,
    private val overlayContainer: FrameLayout,
    private val onConfigChanged: (ScanConfig) -> Unit
) {

    private var controlPanelView: View? = null
    private var currentConfig = ScanConfig()

    // UI组件引用
    private lateinit var etScanFrequency: EditText
    private lateinit var spinnerScanUnit: Spinner
    private lateinit var etDrawFrequency: EditText
    private lateinit var spinnerDrawUnit: Spinner
    private lateinit var etFrameDuration: EditText
    private lateinit var layoutFrameDuration: LinearLayout

    /**
     * 显示控制面板
     */
    fun showControlPanel() {
        if (controlPanelView == null) {
            initializeControlPanel()
        }
        overlayContainer.visibility = View.VISIBLE
        updateUIFromConfig()
    }

    /**
     * 隐藏控制面板
     */
    fun hideControlPanel() {
        overlayContainer.visibility = View.GONE
    }

    /**
     * 初始化控制面板
     */
    private fun initializeControlPanel() {
        val inflater = LayoutInflater.from(context)
        controlPanelView = inflater.inflate(R.layout.control_panel_overlay, overlayContainer, true)

        // 获取UI组件引用
        etScanFrequency = controlPanelView!!.findViewById(R.id.et_scan_frequency)
        spinnerScanUnit = controlPanelView!!.findViewById(R.id.spinner_scan_unit)
        etDrawFrequency = controlPanelView!!.findViewById(R.id.et_draw_frequency)
        spinnerDrawUnit = controlPanelView!!.findViewById(R.id.spinner_draw_unit)

        setupSpinners()
        setupListeners()
    }

    /**
     * 设置下拉框
     */
    private fun setupSpinners() {
        val units = FrequencyUnit.values().map { it.displayName }
        val adapter = ArrayAdapter(context, android.R.layout.simple_spinner_item, units)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)

        spinnerScanUnit.adapter = adapter
        spinnerDrawUnit.adapter = adapter

        // 设置默认选择
        spinnerScanUnit.setSelection(FrequencyUnit.MILLISECONDS.ordinal)
        spinnerDrawUnit.setSelection(FrequencyUnit.FRAMES.ordinal)
    }

    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // 绘制频率变化监听，控制持续时长显示
        etDrawFrequency.addTextChangedListener {
            updateFrameDurationVisibility()
        }

        spinnerDrawUnit.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                updateFrameDurationVisibility()
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // 应用按钮
        controlPanelView!!.findViewById<Button>(R.id.btn_apply_settings).setOnClickListener {
            applySettings()
        }

        // 取消按钮
        controlPanelView!!.findViewById<Button>(R.id.btn_cancel_settings).setOnClickListener {
            hideControlPanel()
        }

        // 点击背景关闭面板
        overlayContainer.setOnClickListener {
            hideControlPanel()
        }

        // 防止点击面板内容时关闭
        controlPanelView!!.setOnClickListener { /* 阻止事件冒泡 */ }
    }

    /**
     * 更新绿框持续时长的显示状态
     */
    private fun updateFrameDurationVisibility() {
        val drawFrequency = etDrawFrequency.text.toString().toIntOrNull() ?: 1
        val drawUnit = FrequencyUnit.values()[spinnerDrawUnit.selectedItemPosition]

        // 如果是每帧绘制，隐藏持续时长设置
        val isEveryFrame = (drawFrequency == 1 && drawUnit == FrequencyUnit.FRAMES)
        layoutFrameDuration.visibility = if (isEveryFrame) View.GONE else View.VISIBLE
    }

    /**
     * 应用设置
     */
    private fun applySettings() {
        try {
            val scanFrequency = etScanFrequency.text.toString().toIntOrNull() ?: 500
            val scanUnit = FrequencyUnit.values()[spinnerScanUnit.selectedItemPosition]
            val drawFrequency = etDrawFrequency.text.toString().toIntOrNull() ?: 5
            val drawUnit = FrequencyUnit.values()[spinnerDrawUnit.selectedItemPosition]

            currentConfig = ScanConfig(
                scanFrequencyValue = scanFrequency,
                scanFrequencyUnit = scanUnit,
                drawFrequencyValue = drawFrequency,
                drawFrequencyUnit = drawUnit,
            )

            onConfigChanged(currentConfig)
            hideControlPanel()

            Toast.makeText(context, "设置已应用", Toast.LENGTH_SHORT).show()

        } catch (e: Exception) {
            Toast.makeText(context, "设置参数有误，请检查输入", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 从配置更新UI
     */
    private fun updateUIFromConfig() {
        etScanFrequency.setText(currentConfig.scanFrequencyValue.toString())
        spinnerScanUnit.setSelection(currentConfig.scanFrequencyUnit.ordinal)
        etDrawFrequency.setText(currentConfig.drawFrequencyValue.toString())
        spinnerDrawUnit.setSelection(currentConfig.drawFrequencyUnit.ordinal)

        updateFrameDurationVisibility()
    }

    /**
     * 更新当前配置
     */
    fun updateConfig(config: ScanConfig) {
        currentConfig = config
        if (controlPanelView != null) {
            updateUIFromConfig()
        }
    }
}