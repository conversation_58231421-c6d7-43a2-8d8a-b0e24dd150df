package com.yancao.qrscanner.domain

/**
 * 扫描配置数据类
 */
data class ScanConfig(
    // 扫描频率配置
    val scanFrequencyValue: Int = 500,
    val scanFrequencyUnit: FrequencyUnit = FrequencyUnit.MILLISECONDS,

    // 绿框绘制频率配置
    val drawFrequencyValue: Int = 5,
    val drawFrequencyUnit: FrequencyUnit = FrequencyUnit.FRAMES,

    // 绿框持续时长（毫秒）
    val frameDurationMs: Long = 2000L
) {

    /**
     * 获取扫描间隔（毫秒）
     */
    fun getScanIntervalMs(): Long {
        return when (scanFrequencyUnit) {
            FrequencyUnit.MILLISECONDS -> scanFrequencyValue.toLong()
            FrequencyUnit.FRAMES -> scanFrequencyValue * 33L // 假设30fps，每帧约33ms
        }
    }

    /**
     * 获取绘制频率（帧数）
     */
    fun getDrawFrequencyFrames(): Int {
        return when (drawFrequencyUnit) {
            FrequencyUnit.FRAMES -> drawFrequencyValue
            FrequencyUnit.MILLISECONDS -> maxOf(1, drawFrequencyValue / 33) // 转换为帧数
        }
    }

    /**
     * 是否每帧都绘制
     */
    fun isDrawEveryFrame(): Boolean {
        return getDrawFrequencyFrames() == 1
    }
}

/**
 * 频率单位枚举
 */
enum class FrequencyUnit(val displayName: String) {
    MILLISECONDS("毫秒"),
    FRAMES("帧数")
}