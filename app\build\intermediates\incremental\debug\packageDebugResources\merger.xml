<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res"><file name="circle_button_selected" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\drawable\circle_button_selected.xml" qualifiers="" type="drawable"/><file name="circle_button_unselected" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\drawable\circle_button_unselected.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="red_border_background" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\drawable\red_border_background.xml" qualifiers="" type="drawable"/><file name="seekbar_background" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\drawable\seekbar_background.xml" qualifiers="" type="drawable"/><file name="zoom_button_selector" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\drawable\zoom_button_selector.xml" qualifiers="" type="drawable"/><file name="activity_main" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="exposure_dark">#FF6B6B</color><color name="exposure_normal">#4ECDC4</color><color name="exposure_bright">#FFE66D</color><color name="semi_transparent_black">#80000000</color><color name="button_primary">#2196F3</color><color name="button_secondary">#FF9800</color><color name="button_accent">#4CAF50</color><color name="button_tertiary">#9C27B0</color><color name="exposure_control_bg">#E0000000</color><color name="text_primary_light">#FFFFFF</color><color name="text_secondary_light">#B3FFFFFF</color></file><file path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">QRscanner</string><string name="exposure_control">曝光控制</string><string name="exposure_value">曝光: %1$.1f EV</string><string name="decrease_exposure">减少曝光</string><string name="increase_exposure">增加曝光</string><string name="reset_exposure">重置曝光</string><string name="optimize_for_scanning">扫码优化</string><string name="low_light_mode">低光模式</string><string name="show_detailed_control">显示详细控制</string><string name="hide_detailed_control">隐藏详细控制</string><string name="exposure_not_supported">设备不支持曝光控制</string><string name="exposure_decreased">曝光已减少</string><string name="exposure_increased">曝光已增加</string><string name="exposure_reset">曝光已重置</string><string name="exposure_optimized">已应用扫码曝光优化</string><string name="exposure_min_reached">已达到最小曝光值</string><string name="exposure_max_reached">已达到最大曝光值</string></file><file path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\values\styles.xml" qualifiers=""><style name="ExposureControlButton">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:layout_margin">2dp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style><style name="ExposureControlText">
        <item name="android:textColor">@color/text_primary_light</item>
        <item name="android:textSize">12sp</item>
    </style><style name="ExposureControlTitle" parent="ExposureControlText">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
    </style></file><file path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.QRscanner" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.QRscanner" parent="Base.Theme.QRscanner"/></file><file path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.QRscanner" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="control_panel_overlay" path="D:\data\AndroidProgram\project\QRscanner\app\src\main\res\layout\control_panel_overlay.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\data\AndroidProgram\project\QRscanner\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\data\AndroidProgram\project\QRscanner\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\data\AndroidProgram\project\QRscanner\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\data\AndroidProgram\project\QRscanner\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>