<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.yancao.qrscanner" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="227" endOffset="51"/></Target><Target id="@+id/settings_overlay" tag="binding_1" view="FrameLayout"><Expressions/><location startLine="158" startOffset="4" endLine="171" endOffset="17"/></Target><Target tag="binding_1" include="control_panel_overlay"><Expressions/><location startLine="169" startOffset="8" endLine="169" endOffset="57"/></Target><Target id="@+id/preview_view" view="androidx.camera.view.PreviewView"><Expressions/><location startLine="9" startOffset="4" endLine="17" endOffset="38"/></Target><Target id="@+id/qr_overlay_view" view="com.yancao.qrscanner.ui.QrCodeOverlayView"><Expressions/><location startLine="20" startOffset="4" endLine="27" endOffset="62"/></Target><Target id="@+id/btn_flashlight" view="Button"><Expressions/><location startLine="32" startOffset="4" endLine="44" endOffset="34"/></Target><Target id="@+id/exposure_control_panel" view="LinearLayout"><Expressions/><location startLine="47" startOffset="4" endLine="109" endOffset="18"/></Target><Target id="@+id/tv_exposure_value" view="TextView"><Expressions/><location startLine="72" startOffset="8" endLine="81" endOffset="47"/></Target><Target id="@+id/mySeekBar" view="com.h6ah4i.android.widget.verticalseekbar.VerticalSeekBar"><Expressions/><location startLine="88" startOffset="12" endLine="95" endOffset="45"/></Target><Target id="@+id/zoom_control_panel" view="FrameLayout"><Expressions/><location startLine="112" startOffset="4" endLine="156" endOffset="17"/></Target><Target id="@+id/zoom_slider_container" view="LinearLayout"><Expressions/><location startLine="122" startOffset="8" endLine="154" endOffset="22"/></Target><Target id="@+id/tv_zoom_ratio" view="TextView"><Expressions/><location startLine="133" startOffset="12" endLine="142" endOffset="51"/></Target><Target id="@+id/seek_bar_zoom" view="SeekBar"><Expressions/><location startLine="145" startOffset="12" endLine="152" endOffset="58"/></Target><Target id="@+id/control_panel" view="LinearLayout"><Expressions/><location startLine="174" startOffset="4" endLine="216" endOffset="18"/></Target><Target id="@+id/button_container" view="LinearLayout"><Expressions/><location startLine="186" startOffset="8" endLine="214" endOffset="22"/></Target><Target id="@+id/btn_scan" view="Button"><Expressions/><location startLine="194" startOffset="12" endLine="202" endOffset="58"/></Target><Target id="@+id/btn_settings" view="Button"><Expressions/><location startLine="204" startOffset="12" endLine="212" endOffset="58"/></Target><Target id="@+id/tv_scan_results" view="TextView"><Expressions/><location startLine="217" startOffset="4" endLine="225" endOffset="43"/></Target></Targets></Layout>