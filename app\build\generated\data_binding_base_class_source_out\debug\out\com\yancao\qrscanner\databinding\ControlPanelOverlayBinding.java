// Generated by view binder compiler. Do not edit!
package com.yancao.qrscanner.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.yancao.qrscanner.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ControlPanelOverlayBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnApplySettings;

  @NonNull
  public final Button btnCancelSettings;

  @NonNull
  public final EditText etDrawFrequency;

  @NonNull
  public final EditText etScanFrequency;

  @NonNull
  public final Spinner spinnerDrawUnit;

  @NonNull
  public final Spinner spinnerScanUnit;

  private ControlPanelOverlayBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnApplySettings, @NonNull Button btnCancelSettings,
      @NonNull EditText etDrawFrequency, @NonNull EditText etScanFrequency,
      @NonNull Spinner spinnerDrawUnit, @NonNull Spinner spinnerScanUnit) {
    this.rootView = rootView;
    this.btnApplySettings = btnApplySettings;
    this.btnCancelSettings = btnCancelSettings;
    this.etDrawFrequency = etDrawFrequency;
    this.etScanFrequency = etScanFrequency;
    this.spinnerDrawUnit = spinnerDrawUnit;
    this.spinnerScanUnit = spinnerScanUnit;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ControlPanelOverlayBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ControlPanelOverlayBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.control_panel_overlay, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ControlPanelOverlayBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_apply_settings;
      Button btnApplySettings = ViewBindings.findChildViewById(rootView, id);
      if (btnApplySettings == null) {
        break missingId;
      }

      id = R.id.btn_cancel_settings;
      Button btnCancelSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnCancelSettings == null) {
        break missingId;
      }

      id = R.id.et_draw_frequency;
      EditText etDrawFrequency = ViewBindings.findChildViewById(rootView, id);
      if (etDrawFrequency == null) {
        break missingId;
      }

      id = R.id.et_scan_frequency;
      EditText etScanFrequency = ViewBindings.findChildViewById(rootView, id);
      if (etScanFrequency == null) {
        break missingId;
      }

      id = R.id.spinner_draw_unit;
      Spinner spinnerDrawUnit = ViewBindings.findChildViewById(rootView, id);
      if (spinnerDrawUnit == null) {
        break missingId;
      }

      id = R.id.spinner_scan_unit;
      Spinner spinnerScanUnit = ViewBindings.findChildViewById(rootView, id);
      if (spinnerScanUnit == null) {
        break missingId;
      }

      return new ControlPanelOverlayBinding((LinearLayout) rootView, btnApplySettings,
          btnCancelSettings, etDrawFrequency, etScanFrequency, spinnerDrawUnit, spinnerScanUnit);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
