<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#E0000000"
    android:padding="16dp"
    android:layout_gravity="center">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="扫描控制面板"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 扫描频率控制 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="扫描频率"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/et_scan_frequency"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="500"
                android:inputType="number"
                android:textColor="@android:color/white"
                android:textColorHint="@android:color/white"
                android:backgroundTint="@android:color/white" />

            <Spinner
                android:id="@+id/spinner_scan_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp" />
        </LinearLayout>
    </LinearLayout>

    <!-- 绿框绘制频率控制 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="绿框绘制频率"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/et_draw_frequency"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="5"
                android:inputType="number"
                android:textColor="@android:color/white"
                android:textColorHint="@android:color/white"
                android:backgroundTint="@android:color/white" />

            <Spinner
                android:id="@+id/spinner_draw_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp" />
        </LinearLayout>
    </LinearLayout>


    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btn_apply_settings"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="应用"
            android:background="@android:color/holo_green_light"
            android:textColor="@android:color/white" />

        <Button
            android:id="@+id/btn_cancel_settings"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="取消"
            android:background="@android:color/holo_red_light"
            android:textColor="@android:color/white" />
    </LinearLayout>

</LinearLayout>